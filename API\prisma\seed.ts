/// <reference types="node" />
import { PrismaClient, UserRole, UserStatus } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting database seeding...");

  // Create default tenant
  const tenant = await prisma.tenant.upsert({
    where: { subdomain: "demo" },
    update: {},
    create: {
      name: "Demo College",
      subdomain: "demo",
      logo_url: null,
      is_active: true,
    },
  });

  console.log("✅ Tenant created:", tenant.name);

  // Create Computer Science course
  const csCourse = await prisma.course.upsert({
    where: { id: 1 },
    update: {},
    create: {
      tenant_id: tenant.id,
      course_name: "Computer Science",
    },
  });

  console.log("✅ Course created:", csCourse.course_name);

  // Create admin user
  const adminPassword = await bcrypt.hash("AdminPass123!", 12);
  const admin = await prisma.user.upsert({
    where: { id: 1 },
    update: {},
    create: {
      tenant_id: tenant.id,
      email: "<EMAIL>",
      password_hash: adminPassword,
      full_name: "System Administrator",
      usn: "ADMIN001",
      role: UserRole.TENANT_ADMIN,
      account_status: UserStatus.APPROVED,
    },
  });

  console.log("✅ Admin user created:", admin.email);

  // Create sample alumni users
  const alumniUsers = [
    {
      email: "<EMAIL>",
      full_name: "John Doe",
      usn: "CS2018001",
      batch_year: 2018,
      company: "Google",
      job_title: "Software Engineer",
      location: "Bangalore, India",
    },
    {
      email: "<EMAIL>",
      full_name: "Jane Smith",
      usn: "CS2017002",
      batch_year: 2017,
      company: "Microsoft",
      job_title: "Senior Software Engineer",
      location: "Hyderabad, India",
    },
  ];

  for (const userData of alumniUsers) {
    const password = await bcrypt.hash("AlumniPass123!", 12);
    const user = await prisma.user.create({
      data: {
        tenant_id: tenant.id,
        email: userData.email,
        password_hash: password,
        full_name: userData.full_name,
        usn: userData.usn,
        role: UserRole.ALUMNUS,
        account_status: UserStatus.APPROVED,
      },
    });

    // Create user profile
    await prisma.userProfile.create({
      data: {
        user_id: user.id,
        tenant_id: tenant.id,
        course_id: csCourse.id,
        batch_year: userData.batch_year,
        current_location: userData.location,
        company: userData.company,
        job_title: userData.job_title,
        privacy_settings: {
          show_email: false,
          show_mobile: false,
        },
      },
    });

    console.log("✅ Alumni user created:", user.email);
  }

  // Create sample student users
  const studentUsers = [
    {
      email: "<EMAIL>",
      full_name: "Rahul Kumar",
      usn: "CS2021001",
      batch_year: 2025,
    },
    {
      email: "<EMAIL>",
      full_name: "Priya Sharma",
      usn: "CS2022002",
      batch_year: 2026,
    },
  ];

  for (const userData of studentUsers) {
    const password = await bcrypt.hash("StudentPass123!", 12);
    const user = await prisma.user.create({
      data: {
        tenant_id: tenant.id,
        email: userData.email,
        password_hash: password,
        full_name: userData.full_name,
        usn: userData.usn,
        role: UserRole.STUDENT,
        account_status: UserStatus.APPROVED,
      },
    });

    // Create user profile
    await prisma.userProfile.create({
      data: {
        user_id: user.id,
        tenant_id: tenant.id,
        course_id: csCourse.id,
        batch_year: userData.batch_year,
        privacy_settings: {
          show_email: false,
          show_mobile: false,
        },
      },
    });

    console.log("✅ Student user created:", user.email);
  }

  console.log("🎉 Database seeding completed successfully!");
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
