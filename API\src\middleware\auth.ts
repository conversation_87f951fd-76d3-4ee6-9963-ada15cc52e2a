/**
 * Authentication Middleware Module
 *
 * This module provides comprehensive authentication and authorization middleware
 * for the IonAlumni API. It handles JWT token validation, user role verification,
 * account status checks, and tenant-based access control.
 *
 * Features:
 * - JWT token authentication
 * - Role-based authorization
 * - Account status validation
 * - Tenant-based access control
 * - Optional authentication for public endpoints
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-01-01
 */

import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { AuthUtils } from "../utils/auth";
import { prisma } from "../config/database";
import { createError } from "./errorHandler";

/**
 * Interface extending Express Request to include authenticated user data
 * This provides type safety for accessing user information in authenticated routes
 */
interface AuthenticatedRequest extends Request {
  user?: {
    userId: string; // User ID as string for JWT payload
    email: string; // User's email address
    role: UserRole; // User's role (STUDENT, ALUMNUS, TENANT_ADMIN, SUPER_ADMIN)
    status: UserStatus; // Account status (PENDING, APPROVED, REJECTED, DEACTIVATED)
    id: string; // User ID as string for database operations
    tenant_id: number; // Tenant ID for multi-tenancy support
  };
}

// Note: Global Request interface extension is defined in src/types/express.d.ts

/**
 * Primary Authentication Middleware
 *
 * Validates JWT tokens and ensures the user is authenticated and authorized to access protected routes.
 * This middleware performs comprehensive security checks including:
 * - JWT token extraction and validation
 * - User existence verification
 * - Account status validation
 * - Tenant activity verification
 *
 * @param req - Express request object (extended with user property)
 * @param res - Express response object
 * @param next - Express next function
 *
 * @throws {401} When token is missing, invalid, or expired
 * @throws {403} When account is deactivated or rejected
 *
 * @example
 * ```typescript
 * router.get('/protected', authenticate, (req, res) => {
 *   // req.user is now available with authenticated user data
 *   res.json({ user: req.user });
 * });
 * ```
 */
export const authenticate = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // Extract JWT token from Authorization header (Bearer token format)
    const token = AuthUtils.extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      throw createError("Access token is required", 401);
    }

    // Verify and decode the JWT token
    const payload = AuthUtils.verifyAccessToken(token);

    // Fetch user from database with tenant information for security validation
    const user = await prisma.user.findUnique({
      where: { id: parseInt(payload.userId) },
      include: {
        tenant: {
          select: {
            is_active: true,
          },
        },
      },
    });

    // Ensure user exists and belongs to an active tenant
    if (!user || !user.tenant.is_active) {
      throw createError("User not found or tenant inactive", 401);
    }

    // Check account status - prevent access for deactivated accounts
    if (user.account_status === UserStatus.DEACTIVATED) {
      throw createError("Account has been deactivated", 403);
    }

    // Check account status - prevent access for rejected accounts
    if (user.account_status === UserStatus.REJECTED) {
      throw createError("Account access has been denied", 403);
    }

    // Attach authenticated user information to request object for downstream middleware/controllers
    req.user = {
      userId: payload.userId,
      email: user.email,
      role: user.role,
      status: user.account_status,
      id: user.id.toString(),
      tenant_id: user.tenant_id,
    };

    next();
  } catch (error) {
    // Handle JWT-specific errors with appropriate HTTP status codes
    if (error instanceof Error) {
      if (error.name === "JsonWebTokenError") {
        return next(createError("Invalid token", 401));
      }
      if (error.name === "TokenExpiredError") {
        return next(createError("Token expired", 401));
      }
    }
    next(error);
  }
};

/**
 * Account Approval Verification Middleware
 *
 * Ensures that the authenticated user's account has been approved by an administrator.
 * This middleware should be used after authentication for routes that require approved accounts.
 *
 * @param req - Express request object with authenticated user
 * @param res - Express response object
 * @param next - Express next function
 *
 * @throws {401} When user is not authenticated
 * @throws {403} When account is not approved
 *
 * @example
 * ```typescript
 * router.get('/directory', authenticate, requireApproved, getUserDirectory);
 * ```
 */
export const requireApproved = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next(createError("Authentication required", 401));
  }

  if (req.user.status !== UserStatus.APPROVED) {
    return next(createError("Account pending approval", 403));
  }

  next();
};

/**
 * Role-Based Authorization Middleware Factory
 *
 * Creates middleware that restricts access to users with specific roles.
 * This is a higher-order function that returns middleware configured for the specified roles.
 *
 * @param roles - Array of UserRole enums that are allowed to access the route
 * @returns Express middleware function
 *
 * @throws {401} When user is not authenticated
 * @throws {403} When user doesn't have required role
 *
 * @example
 * ```typescript
 * // Allow only admins
 * router.delete('/users/:id', authenticate, authorize(UserRole.SUPER_ADMIN), deleteUser);
 *
 * // Allow multiple roles
 * router.get('/admin-panel', authenticate, authorize(UserRole.TENANT_ADMIN, UserRole.SUPER_ADMIN), getAdminPanel);
 * ```
 */
export const authorize = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError("Authentication required", 401));
    }

    if (!roles.includes(req.user.role as UserRole)) {
      return next(createError("Insufficient permissions", 403));
    }

    next();
  };
};

// =============================================================================
// Pre-configured Role-Based Authorization Middleware
// =============================================================================

/**
 * Tenant Administrator Authorization Middleware
 *
 * Restricts access to tenant administrators and super administrators.
 * Used for tenant-level administrative operations like user management,
 * content moderation, and tenant configuration.
 */
export const requireTenantAdmin = authorize(UserRole.TENANT_ADMIN, UserRole.SUPER_ADMIN);

/**
 * Super Administrator Authorization Middleware
 *
 * Restricts access to super administrators only.
 * Used for system-wide operations like tenant management,
 * global configuration, and platform administration.
 */
export const requireSuperAdmin = authorize(UserRole.SUPER_ADMIN);

/**
 * Alumni Authorization Middleware
 *
 * Restricts access to alumni users only.
 * Used for alumni-specific features like mentorship programs,
 * career services, and alumni-only content.
 */
export const requireAlumni = authorize(UserRole.ALUMNUS);

/**
 * Student Authorization Middleware
 *
 * Restricts access to current students only.
 * Used for student-specific features like course materials,
 * academic resources, and student-only events.
 */
export const requireStudent = authorize(UserRole.STUDENT);

/**
 * Alumni or Administrator Authorization Middleware
 *
 * Allows access to alumni and administrators.
 * Used for features that require graduated status or administrative privileges,
 * such as job postings, professional networking, and career guidance.
 */
export const requireAlumniOrAdmin = authorize(UserRole.ALUMNUS, UserRole.TENANT_ADMIN, UserRole.SUPER_ADMIN);

/**
 * Student or Alumni Authorization Middleware
 *
 * Allows access to both current students and alumni (excludes admin-only access).
 * Used for general community features like forums, events, and networking
 * that are available to all community members regardless of graduation status.
 */
export const requireStudentOrAlumni = authorize(UserRole.STUDENT, UserRole.ALUMNUS);

/**
 * Optional Authentication Middleware
 *
 * Provides optional authentication for routes that can be accessed by both
 * authenticated and unauthenticated users. If a valid token is provided,
 * the user information is attached to the request. If no token is provided
 * or the token is invalid, the request continues without user information.
 *
 * This is useful for public endpoints that may show different content
 * based on authentication status (e.g., public posts with personalized features
 * for authenticated users).
 *
 * @param req - Express request object (may or may not have user property set)
 * @param res - Express response object
 * @param next - Express next function
 *
 * @example
 * ```typescript
 * // Public endpoint with optional personalization
 * router.get('/public-posts', optionalAuth, (req, res) => {
 *   const isAuthenticated = !!req.user;
 *   // Show different content based on authentication status
 * });
 * ```
 */
export const optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // Attempt to extract token from Authorization header
    const token = AuthUtils.extractTokenFromHeader(req.headers.authorization);

    if (token) {
      // Verify token and fetch user information
      const payload = AuthUtils.verifyAccessToken(token);

      const user = await prisma.user.findUnique({
        where: { id: parseInt(payload.userId) },
        include: {
          tenant: {
            select: {
              is_active: true,
            },
          },
        },
      });

      // Only attach user info if user exists, is approved, and tenant is active
      if (user && user.account_status === UserStatus.APPROVED && user.tenant.is_active) {
        req.user = {
          userId: payload.userId,
          email: user.email,
          role: user.role,
          status: user.account_status,
          id: user.id.toString(),
          tenant_id: user.tenant_id,
        };
      }
    }
  } catch (error) {
    // Silently ignore authentication errors for optional auth
    // This allows the request to continue without user information
  }

  next();
};
