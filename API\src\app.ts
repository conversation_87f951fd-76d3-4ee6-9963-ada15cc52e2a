import cookieParser from "cookie-parser";
import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import helmet from "helmet";
import { createServer } from "http";
import morgan from "morgan";
import path from "path";

// Load environment variables
dotenv.config();

// Import configurations and middleware
import { config } from "./config/config";
import { corsOptions } from "./config/cors";
import { swaggerSpec, swaggerUi, swaggerUiOptions } from "./config/swagger";
import { errorHandler } from "./middleware/errorHandler";
import { notFoundHandler } from "./middleware/notFoundHandler";
import { clearRateLimitCache, rateLimiter } from "./middleware/rateLimiter";
// import { enhancedRateLimiter, progressiveSlowDown, initializeRateLimiting } from "./middleware/distributedRateLimiter";
// import { initializeRedis } from "./config/redis";
// import { DistributedCacheService } from "./services/distributedCacheService";
import { FileService } from "./services/fileService";
import { Logger } from "./services/loggerService";
// import HealthCheckService from "./services/healthCheckService";

// Import routes
import authRoutes from "./routes/auth";
import userRoutes from "./routes/user";
// import monitoringRoutes from "./routes/monitoring";

// Import Socket.IO configuration and handlers
import { initializeSocket } from "./config/socket";
import { initializeSocketHandlers } from "./handlers/socketHandlers";

const app = express();
const PORT = parseInt(process.env.PORT || "5000");

// Initialize services (Redis disabled for now)
const initializeServices = async () => {
  try {
    // if (process.env.ENABLE_DISTRIBUTED_CACHE === "true") {
    //   await initializeRedis();
    //   DistributedCacheService.init();
    // }
    // initializeRateLimiting();
    // HealthCheckService.init();

    // Initialize file service cleanup
    FileService.initializeCleanup();

    Logger.info("Basic services initialized successfully");
  } catch (error) {
    Logger.error("Failed to initialize services:", error);
    process.exit(1);
  }
};

// Create HTTP server with enhanced configuration
const server = createServer(app);

// Initialize Socket.IO
const io = initializeSocket(server);

// Configure server for high concurrency
server.keepAliveTimeout = parseInt(process.env.KEEP_ALIVE_TIMEOUT || "65000");
server.headersTimeout = parseInt(process.env.HEADERS_TIMEOUT || "66000");

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: process.env.NODE_ENV === "production",
  })
);

// Response compression removed - package was uninstalled

// CORS configuration
app.use(cors(corsOptions));

// Body parsing middleware (optimized for performance)
app.use(
  express.json({
    limit: "10mb",
    strict: true,
    type: ["application/json", "application/*+json"],
  })
);
app.use(
  express.urlencoded({
    extended: true,
    limit: "10mb",
    parameterLimit: 1000,
  })
);

// Cookie parser
app.use(cookieParser());

// Logging middleware
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
} else {
  app.use(morgan("combined"));
}

// Rate limiting (using existing implementation for now)
app.use(rateLimiter);

// Performance monitoring middleware removed for simplicity

// API optimization middleware removed for simplicity

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     tags: [System]
 *     security: []
 *     responses:
 *       200:
 *         description: API is healthy and running
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: OK
 *                 message:
 *                   type: string
 *                   example: Alumni Portal API is running
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 environment:
 *                   type: string
 *                   example: development
 *                 uptime:
 *                   type: number
 *                   example: 123.456
 */
app.get("/health", (_req, res) => {
  res.status(200).json({
    status: "OK",
    message: "Alumni Portal API is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    uptime: process.uptime(),
  });
});

// Readiness check (for Kubernetes)
app.get("/ready", (_req, res) => {
  res.status(200).json({
    status: "ready",
    timestamp: new Date().toISOString(),
  });
});

// Liveness check (for Kubernetes)
app.get("/live", (_req, res) => {
  res.status(200).json({
    status: "alive",
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
  });
});

/**
 * @swagger
 * /dev/clear-rate-limits:
 *   post:
 *     summary: Clear rate limit cache (Development only)
 *     tags: [Development]
 *     security: []
 *     responses:
 *       200:
 *         description: Rate limit cache cleared successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Rate limit cache cleared
 *       403:
 *         description: Not available in production
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: This endpoint is only available in development
 */
// Development endpoint to clear rate limits
if (process.env.NODE_ENV === "development") {
  app.post("/dev/clear-rate-limits", (_req, res) => {
    const cleared = clearRateLimitCache();
    if (cleared) {
      res.status(200).json({
        success: true,
        message: "Rate limit cache cleared",
      });
    } else {
      res.status(403).json({
        error: "This endpoint is only available in development",
      });
    }
  });
}

// Swagger API Documentation
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));

// Swagger JSON endpoint
app.get("/api-docs.json", (_req, res) => {
  res.setHeader("Content-Type", "application/json");
  res.send(swaggerSpec);
});

// Serve uploaded files as static content
const uploadsPath = path.resolve(__dirname, "../", config.upload.uploadPath);
app.use("/uploads", express.static(uploadsPath));

// !API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
// app.use("/api/socket", socketRoutes); // Temporarily disabled for testing
// app.use("/api/monitoring", monitoringRoutes);

// 404 handler Middleware
app.use(notFoundHandler);

// Global error handler Middleware
app.use(errorHandler);

// Start HTTP server with service initialization
const startServer = async () => {
  try {
    // Initialize services first
    await initializeServices();

    // Initialize Socket.IO handlers
    initializeSocketHandlers(io);

    // Start server
    server.listen(PORT, () => {
      console.log(`🚀 Alumni Portal API server running on port http://localhost:${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV}`);
      console.log(`🔌 Socket.IO server initialized and ready`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

startServer();

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully");
  server.close(() => {
    console.log("HTTP server closed");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("SIGINT received, shutting down gracefully");
  server.close(() => {
    console.log("HTTP server closed");
    process.exit(0);
  });
});

export default app;
