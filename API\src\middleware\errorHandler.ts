/**
 * Error Handling Middleware Module
 *
 * This module provides centralized error handling for the IonAlumni API.
 * It processes various types of errors (database, validation, authentication, etc.)
 * and returns consistent, user-friendly error responses.
 *
 * Features:
 * - Prisma database error handling
 * - JWT authentication error handling
 * - File upload error handling
 * - Custom application error handling
 * - Development vs production error responses
 * - Structured error logging
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-01-01
 */

import { Request, Response, NextFunction } from "express";

/**
 * Extended Error interface for application-specific errors
 *
 * @interface AppError
 * @extends Error
 */
export interface AppError extends Error {
  statusCode?: number; // HTTP status code for the error
  isOperational?: boolean; // Flag to distinguish operational vs programming errors
}

/**
 * Global Error Handler Middleware
 *
 * Centralized error handling middleware that processes all errors thrown in the application.
 * It categorizes errors by type and returns appropriate HTTP status codes and messages.
 *
 * Error Types Handled:
 * - Prisma database errors (P2002, P2025, P2003, etc.)
 * - JWT authentication errors
 * - File upload errors (Multer)
 * - Custom application errors
 * - Validation errors
 *
 * @param err - The error object (can be AppError or standard Error)
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function (unused but required for error middleware signature)
 *
 * @example
 * ```typescript
 * // Usage in app.ts
 * app.use(errorHandler);
 * ```
 */
export const errorHandler = (err: AppError | Error, req: Request, res: Response, next: NextFunction) => {
  let statusCode = 500;
  let message = "Internal Server Error";
  let details: any = null;

  // =============================================================================
  // Database Error Handling (Prisma)
  // =============================================================================

  if (err.name === "PrismaClientKnownRequestError") {
    const prismaError = err as any;
    switch (prismaError.code) {
      case "P2002":
        // Unique constraint violation
        statusCode = 409;
        message = "A record with this information already exists";
        details = { field: prismaError.meta?.target };
        break;
      case "P2025":
        // Record not found
        statusCode = 404;
        message = "Record not found";
        break;
      case "P2003":
        // Foreign key constraint violation
        statusCode = 400;
        message = "Foreign key constraint failed";
        break;
      default:
        statusCode = 400;
        message = "Database operation failed";
    }
  }

  // =============================================================================
  // Prisma Validation Errors
  // =============================================================================
  else if (err.name === "PrismaClientValidationError") {
    statusCode = 400;
    message = "Invalid data provided";
  }

  // =============================================================================
  // Custom Application Errors
  // =============================================================================
  else if ("statusCode" in err && err.statusCode) {
    statusCode = err.statusCode;
    message = err.message;
  }

  // =============================================================================
  // Validation Errors
  // =============================================================================
  else if (err.name === "ValidationError") {
    statusCode = 400;
    message = "Validation failed";
  }

  // =============================================================================
  // JWT Authentication Errors
  // =============================================================================
  else if (err.name === "JsonWebTokenError") {
    statusCode = 401;
    message = "Invalid token";
  } else if (err.name === "TokenExpiredError") {
    statusCode = 401;
    message = "Token expired";
  }

  // =============================================================================
  // File Upload Errors (Multer)
  // =============================================================================
  else if (err.name === "MulterError") {
    statusCode = 400;
    message = "File upload error";
    if (err.message.includes("File too large")) {
      message = "File size too large";
    }
  }

  // =============================================================================
  // Error Logging and Response Generation
  // =============================================================================

  // Log detailed error information in development environment
  if (process.env.NODE_ENV === "development") {
    console.error("🚨 Error Details:", {
      name: err.name,
      message: err.message,
      stack: err.stack,
      statusCode,
      path: req.path,
      method: req.method,
    });
  }

  // Construct standardized error response
  const errorResponse: any = {
    error: message,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
  };

  // Include additional error details if available
  if (details) {
    errorResponse.details = details;
  }

  // Include stack trace in development for debugging
  if (process.env.NODE_ENV === "development") {
    errorResponse.stack = err.stack;
  }

  // Send error response with appropriate HTTP status code
  res.status(statusCode).json(errorResponse);
};

/**
 * Error Factory Function
 *
 * Creates standardized application errors with consistent structure.
 * These errors are designed to be caught by the global error handler
 * and converted into appropriate HTTP responses.
 *
 * @param message - Human-readable error message
 * @param statusCode - HTTP status code (defaults to 500)
 * @returns AppError object with statusCode and operational flag
 *
 * @example
 * ```typescript
 * // Create a validation error
 * throw createError("Invalid email format", 400);
 *
 * // Create an authorization error
 * throw createError("Access denied", 403);
 *
 * // Create a not found error
 * throw createError("User not found", 404);
 * ```
 */
export const createError = (message: string, statusCode: number = 500): AppError => {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.isOperational = true; // Mark as operational error (not a programming bug)
  return error;
};
